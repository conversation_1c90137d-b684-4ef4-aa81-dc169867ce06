// 故事生成器组件
// 提供主题选择和故事生成功能

import { useState } from 'react';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Alert, AlertDescription } from './ui/alert';
import { Badge } from './ui/badge';
import { Spinner } from './ui/spinner';
import storyGeneratorService from '../services/storyGeneratorService';
import storyIllustrationGenerator from '../services/storyIllustrationGenerator';

interface StoryGeneratorProps {
  onStoryGenerated: (storyData: any) => void;
  onCancel: () => void;
}

export function StoryGenerator({ onStoryGenerated, onCancel }: StoryGeneratorProps) {
  const [selectedTheme, setSelectedTheme] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [isGeneratingImages, setIsGeneratingImages] = useState(false);
  const [error, setError] = useState<string>('');
  const [generationStep, setGenerationStep] = useState<string>('');
  const [imageProgress, setImageProgress] = useState<any>(null);

  const themes = storyGeneratorService.getAvailableThemes();
  const isServiceAvailable = storyGeneratorService.isServiceAvailable();
  const isImageServiceAvailable = storyIllustrationGenerator.isServiceAvailable();

  const handleThemeSelect = (theme: string) => {
    setSelectedTheme(theme);
    setError('');
  };

  const handleGenerateStory = async () => {
    if (!selectedTheme) {
      setError('请先选择一个故事主题');
      return;
    }

    if (!isServiceAvailable) {
      setError('OpenAI API密钥未配置，请在.env文件中设置VITE_OPENAI_API_KEY');
      return;
    }

    setIsGenerating(true);
    setError('');
    setGenerationStep('正在生成故事内容...');

    try {
      console.log(`🎨 开始生成主题为"${selectedTheme}"的故事...`);

      const storyData = await storyGeneratorService.generateStory(selectedTheme);

      console.log('✅ 故事生成成功:', storyData.title);
      setGenerationStep('故事内容生成完成');

      // 检查是否可以生成插图
      if (isImageServiceAvailable) {
        setIsGeneratingImages(true);
        setGenerationStep('正在生成故事插图...');

        try {
          console.log('🖼️ 开始生成故事插图...');

          const generatedImages = await storyIllustrationGenerator.generateStoryIllustrations(
            storyData,
            (progress) => {
              setImageProgress(progress);
              setGenerationStep(`正在生成第${progress.currentPage}页插图... (${progress.current}/${progress.total})`);
            }
          );

          // 将生成的插图URL更新到故事数据中
          const updatedStoryData = {
            ...storyData,
            pages: storyData.pages.map(page => {
              if (generatedImages[page.id]) {
                return {
                  ...page,
                  imagePath: generatedImages[page.id],
                  image: generatedImages[page.id]
                };
              }
              return page;
            })
          };

          console.log('🎉 故事和插图生成完成!');
          onStoryGenerated(updatedStoryData);

        } catch (imageError) {
          console.warn('⚠️ 插图生成失败，但故事内容已生成:', imageError);
          // 即使插图生成失败，也返回故事内容
          onStoryGenerated(storyData);
        }
      } else {
        console.log('⚠️ LiblibAI服务未初始化，跳过插图生成');
        onStoryGenerated(storyData);
      }

    } catch (err: any) {
      console.error('❌ 故事生成失败:', err);
      setError(err.message || '故事生成失败，请重试');
    } finally {
      setIsGenerating(false);
      setIsGeneratingImages(false);
      setGenerationStep('');
      setImageProgress(null);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-amber-50">
      <Card className="max-w-2xl w-full">
        <CardHeader>
          <CardTitle className="text-3xl text-center">
            🎨 AI故事生成器
          </CardTitle>
          <p className="text-center text-gray-600">
            为自闭症儿童生成个性化的教育绘本故事
          </p>
        </CardHeader>
        <CardContent>
          {/* API密钥状态提示 */}
          {!isServiceAvailable && (
            <Alert className="mb-6">
              <AlertDescription>
                <p>OpenAI API密钥未配置。请在项目根目录的.env文件中设置：</p>
                <code className="block mt-2 p-2 bg-gray-100 rounded text-sm">
                  VITE_OPENAI_API_KEY=your_openai_api_key_here
                </code>
                <p className="mt-2 text-sm text-gray-600">
                  设置完成后请重启开发服务器。
                </p>
              </AlertDescription>
            </Alert>
          )}

          {/* 主题选择 */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-4">选择故事主题</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {themes.map((theme) => (
                <Card 
                  key={theme}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    selectedTheme === theme 
                      ? 'ring-2 ring-blue-500 bg-blue-50' 
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => handleThemeSelect(theme)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium text-lg">{theme}</h4>
                        <p className="text-sm text-gray-600 mt-1">
                          {storyGeneratorService.getThemeDescription(theme)}
                        </p>
                      </div>
                      {selectedTheme === theme && (
                        <Badge variant="default">已选择</Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* 错误提示 */}
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* 操作按钮 */}
          <div className="flex justify-center gap-4">
            <Button
              onClick={handleGenerateStory}
              disabled={isGenerating || !selectedTheme || !isServiceAvailable}
              size="lg"
            >
              {isGenerating ? (
                <>
                  <Spinner className="mr-2" />
                  {isGeneratingImages ? '生成插图中...' : '生成故事中...'}
                </>
              ) : (
                '🎨 生成故事'
              )}
            </Button>
            
            <Button 
              onClick={onCancel}
              variant="outline"
              size="lg"
              disabled={isGenerating}
            >
              返回
            </Button>
          </div>

          {/* 生成进度提示 */}
          {isGenerating && (
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <div className="text-center">
                <p className="text-blue-800 font-medium">{generationStep || '正在生成故事...'}</p>
                <p className="text-blue-600 text-sm mt-1">
                  AI正在为您创作一个关于"{selectedTheme}"的精彩故事，请稍候
                </p>

                {/* 插图生成进度 */}
                {isGeneratingImages && imageProgress && (
                  <div className="mt-3">
                    <div className="flex justify-between text-xs text-blue-600 mb-1">
                      <span>插图生成进度</span>
                      <span>{imageProgress.current}/{imageProgress.total}</span>
                    </div>
                    <div className="w-full bg-blue-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${(imageProgress.current / imageProgress.total) * 100}%` }}
                      ></div>
                    </div>
                    {imageProgress.status === 'completed' && imageProgress.imageUrl && (
                      <p className="text-xs text-green-600 mt-1">
                        ✅ 第{imageProgress.currentPage}页插图生成完成
                      </p>
                    )}
                    {imageProgress.status === 'error' && (
                      <p className="text-xs text-red-600 mt-1">
                        ❌ 第{imageProgress.currentPage}页插图生成失败
                      </p>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 功能说明 */}
          <div className="mt-8 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium mb-2">功能特点</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 针对自闭症儿童特点设计的故事内容</li>
              <li>• 12页完整故事，包含3个交互环节</li>
              <li>• 简单清晰的语言，避免复杂修辞</li>
              <li>• 明确的情感表达和社交互动场景</li>
              <li>• 配套的引导提示帮助儿童参与互动</li>
              <li>• 自动生成配套插图，视觉效果更佳</li>
            </ul>

            <h4 className="font-medium mb-2 mt-4">配置说明</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• API密钥通过环境变量配置，更加安全</li>
              <li>• 无需在界面中输入敏感信息</li>
              <li>• 支持开发和生产环境的不同配置</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
