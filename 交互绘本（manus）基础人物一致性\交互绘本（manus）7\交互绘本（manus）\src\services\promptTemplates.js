// 为自闭症儿童语音交互绘本设计的提示词模板

// 故事生成提示词模板
const STORY_PROMPT_TEMPLATE = `
请为{age_range}岁的自闭症儿童创作一个以"{theme}"为主题的12页绘本故事。

要求：
1. 故事应该简单、清晰，使用具体而非抽象的语言
2. 每页内容控制在100-150字左右
3. 使用简单的句子结构和明确的因果关系
4. 避免使用隐喻、反语或复杂的修辞手法
5. 故事中应包含明确的情感表达和社交互动场景
6. 在故事中随机安排3个交互环节（第4页、第8页和第11页），这些页面需要设计问题
7. 交互问题应鼓励自闭症儿童进行语言表达，不是简单的选择题
8. 每个交互问题都需要配备一个引导提示，用于在儿童30秒内没有回答时提供帮助

输出格式：
{
  "title": "故事标题",
  "characters": [
    {
      "name": "角色名称",
      "description": "角色外貌描述",
      "personality": "性格特点"
    }
  ],
  "pages": [
    {
      "page_number": 1,
      "content": "第1页内容...",
      "is_interactive": false,
      "image_keywords": {
        "characters": ["角色1外貌", "角色2外貌"],
        "scene": "场景描述",
        "mood": "情绪氛围",
        "actions": ["动作1", "动作2"],
        "objects": ["物品1", "物品2"],
        "style": "儿童插画风格"
      }
    },
    {
      "page_number": 4,
      "content": "第4页内容...",
      "is_interactive": true,
      "interactive_question": "问题内容...",
      "guidance_prompt": "引导提示内容...",
      "image_keywords": {
        "characters": ["角色1外貌", "角色2外貌"],
        "scene": "场景描述",
        "mood": "情绪氛围",
        "actions": ["动作1", "动作2"],
        "objects": ["物品1", "物品2"],
        "style": "儿童插画风格"
      }
    }
    // 其他页面...
  ]
}
`;

// 针对不同主题的专门提示词模板
const THEME_SPECIFIC_PROMPTS = {
  "人际关系": `
请为{age_range}岁的自闭症儿童创作一个关于"人际关系"的12页绘本故事。

故事主题要求：
- 重点展现如何与他人建立友谊
- 包含分享、合作、互助的情节
- 展示如何处理与朋友的小冲突
- 教导基本的社交礼仪和沟通技巧
- 强调倾听和理解他人感受的重要性

角色设定建议：
- 主角：一个善良但有些内向的小动物
- 配角：不同性格的朋友们
- 情境：学校、游乐场、家庭聚会等社交场所

具体要求：
1. 故事应该简单、清晰，使用具体而非抽象的语言
2. 每页内容控制在100-150字左右
3. 使用简单的句子结构和明确的因果关系
4. 避免使用隐喻、反语或复杂的修辞手法
5. 故事中应包含明确的情感表达和社交互动场景
6. 在第4页、第8页和第11页设计交互环节
7. 交互问题必须专门针对自闭症儿童的病症特征设计，包括：
   - 社交沟通困难：设计渐进式社交技能训练问题
   - 感觉处理差异：考虑感官敏感性，避免过度刺激的情境
   - 重复行为和特殊兴趣：利用这些特点设计吸引注意的问题
   - 变化适应困难：提供结构化、可预测的问题框架
   - 抽象思维挑战：使用具体、视觉化的问题描述
8. 每个交互问题都需要配备一个引导提示，使用分步骤的引导方式
9. 交互问题设计原则：
   - 第4页：社交启动技能（如何开始交流、克服社交焦虑）
   - 第8页：群体融入技能（理解社交情境、适应群体动态）
   - 第11页：反思与泛化技能（总结经验、应用到生活中）
10. 问题复杂度要求：
    - 包含多个子问题，训练序列思维
    - 要求因果关系分析，提升逻辑推理
    - 鼓励个人经历分享，促进自我表达
    - 提供具体情境，避免抽象概念

图像关键词要求：
为每页生成适合AI图像生成的详细英文关键词，严格按照以下格式：
- characters: 角色的详细外貌描述（英文），包括：物种、毛色/肤色、服装颜色和款式、表情、姿态等
- scene: 具体场景环境（英文），如：sunny forest clearing, cozy home interior, bright classroom等
- mood: 情绪氛围词汇（英文），如：warm and friendly, joyful and excited, peaceful and calm等
- actions: 具体动作描述（英文），如：walking together, sharing toys, reading books等
- objects: 场景中的物品（英文），如：colorful flowers, wooden toys, picture books等
- lighting: 光照效果（英文），如：soft natural lighting, warm golden hour, bright daylight等
- style: 固定使用"children's book illustration, watercolor style, soft pastel colors"

注意：所有关键词必须使用英文，要具体详细，适合Stable Diffusion等AI图像生成模型

输出格式：
{
  "title": "故事标题",
  "ageGroup": "{age_range}",
  "theme": "人际关系",
  "characters": [
    {
      "name": "角色名称",
      "description": "角色外貌描述",
      "personality": "性格特点"
    }
  ],
  "pages": [
    {
      "id": 1,
      "content": "第1页内容...",
      "isInteractive": false,
      "image_keywords": {
        "characters": ["white rabbit with blue eyes, wearing yellow apron, happy expression", "brown bear cub, red shirt, friendly smile"],
        "scene": "sunny forest clearing with tall trees",
        "mood": "warm and friendly, joyful atmosphere",
        "actions": ["walking on forest path", "exploring nature"],
        "objects": ["green grass", "colorful wildflowers", "tree branches"],
        "lighting": "soft natural lighting, dappled sunlight",
        "style": "children's book illustration, watercolor style, soft pastel colors"
      }
    },
    {
      "id": 4,
      "content": "第4页内容...",
      "isInteractive": true,
      "interactiveQuestion": "问题内容...",
      "guidancePrompt": "引导提示内容...",
      "image_keywords": {
        "characters": ["white rabbit with blue eyes, wearing yellow apron, excited expression", "brown bear cub, red shirt, helping gesture"],
        "scene": "bright classroom with wooden desks",
        "mood": "encouraging and supportive, learning atmosphere",
        "actions": ["raising hand to answer", "sharing with friends"],
        "objects": ["colorful books", "wooden chairs", "blackboard"],
        "lighting": "bright classroom lighting, warm indoor glow",
        "style": "children's book illustration, watercolor style, soft pastel colors"
      }
    }
    // 其他页面...
  ]
}
`,

  "家庭生活": `
请为{age_range}岁的自闭症儿童创作一个关于"家庭生活"的12页绘本故事。

故事主题要求：
- 展现温馨的家庭日常生活
- 包含家庭成员之间的关爱和支持
- 教导家庭责任和基本的家务参与
- 展示如何表达对家人的爱和感谢
- 处理家庭中的小问题和情感交流

角色设定建议：
- 主角：一个可爱的小动物孩子
- 配角：爸爸、妈妈、兄弟姐妹、爷爷奶奶等
- 情境：家中的客厅、厨房、卧室、花园等

具体要求：
1. 故事应该简单、清晰，使用具体而非抽象的语言
2. 每页内容控制在100-150字左右
3. 使用简单的句子结构和明确的因果关系
4. 避免使用隐喻、反语或复杂的修辞手法
5. 故事中应包含明确的情感表达和家庭互动场景
6. 在第4页、第8页和第11页设计交互环节
7. 交互问题必须专门针对自闭症儿童的病症特征设计，包括：
   - 社交沟通困难：设计渐进式社交技能训练问题
   - 感觉处理差异：考虑感官敏感性，避免过度刺激的情境
   - 重复行为和特殊兴趣：利用这些特点设计吸引注意的问题
   - 变化适应困难：提供结构化、可预测的问题框架
   - 抽象思维挑战：使用具体、视觉化的问题描述
8. 每个交互问题都需要配备一个引导提示，使用分步骤的引导方式
9. 交互问题设计原则：
   - 第4页：家庭责任认知（理解家庭角色、培养责任感）
   - 第8页：情感表达技能（学会表达爱意、处理家庭冲突）
   - 第11页：家庭价值观内化（理解家庭重要性、感恩表达）
10. 问题复杂度要求：
    - 包含多个子问题，训练序列思维
    - 要求因果关系分析，提升逻辑推理
    - 鼓励个人经历分享，促进自我表达
    - 提供具体情境，避免抽象概念

图像关键词要求：
为每页生成适合图像生成的关键词，包括：
- characters: 角色的具体外貌描述（发色、服装、表情等）
- scene: 场景环境描述（室内/户外、具体地点）
- mood: 情绪氛围（温馨、和谐、快乐等）
- actions: 具体动作描述（拥抱、做饭、游戏等）
- objects: 相关物品（家具、玩具、食物等）
- style: 统一使用"温暖儿童插画风格，柔和色彩"

输出格式：
{
  "title": "故事标题",
  "ageGroup": "{age_range}",
  "theme": "家庭生活",
  "characters": [
    {
      "name": "角色名称",
      "description": "角色外貌描述",
      "personality": "性格特点"
    }
  ],
  "pages": [
    {
      "id": 1,
      "content": "第1页内容...",
      "isInteractive": false,
      "image_keywords": {
        "characters": ["角色1外貌", "角色2外貌"],
        "scene": "场景描述",
        "mood": "情绪氛围",
        "actions": ["动作1", "动作2"],
        "objects": ["物品1", "物品2"],
        "style": "温暖儿童插画风格，柔和色彩"
      }
    },
    {
      "id": 4,
      "content": "第4页内容...",
      "isInteractive": true,
      "interactiveQuestion": "问题内容...",
      "guidancePrompt": "引导提示内容...",
      "image_keywords": {
        "characters": ["角色1外貌", "角色2外貌"],
        "scene": "场景描述",
        "mood": "情绪氛围",
        "actions": ["动作1", "动作2"],
        "objects": ["物品1", "物品2"],
        "style": "温暖儿童插画风格，柔和色彩"
      }
    }
    // 其他页面...
  ]
}
`,

  "法律常识": `
请为{age_range}岁的自闭症儿童创作一个关于"法律常识"的12页绘本故事。

故事主题要求：
- 用简单易懂的方式介绍基本的法律概念
- 教导什么是对的行为，什么是错的行为
- 包含保护自己和他人的基本规则
- 展示遵守规则的重要性
- 教导如何在遇到问题时寻求帮助

角色设定建议：
- 主角：一个聪明好学的小动物
- 配角：警察叔叔、老师、家长等权威人物
- 情境：学校、公园、商店、马路等公共场所

具体要求：
1. 故事应该简单、清晰，使用具体而非抽象的语言
2. 每页内容控制在100-150字左右
3. 使用简单的句子结构和明确的因果关系
4. 避免使用隐喻、反语或复杂的修辞手法
5. 故事中应包含明确的规则教育和安全意识
6. 在第4页、第8页和第11页设计交互环节
7. 交互问题必须专门针对自闭症儿童的病症特征设计，包括：
   - 社交沟通困难：设计渐进式社交技能训练问题
   - 感觉处理差异：考虑感官敏感性，避免过度刺激的情境
   - 重复行为和特殊兴趣：利用这些特点设计吸引注意的问题
   - 变化适应困难：提供结构化、可预测的问题框架
   - 抽象思维挑战：使用具体、视觉化的问题描述
8. 每个交互问题都需要配备一个引导提示，使用分步骤的引导方式
9. 交互问题设计原则：
   - 第4页：规则理解认知（理解基本规则、安全意识培养）
   - 第8页：行为判断技能（区分对错行为、后果分析）
   - 第11页：规则应用能力（在生活中应用规则、寻求帮助）
10. 问题复杂度要求：
    - 包含多个子问题，训练序列思维
    - 要求因果关系分析，提升逻辑推理
    - 鼓励个人经历分享，促进自我表达
    - 提供具体情境，避免抽象概念

图像关键词要求：
为每页生成适合图像生成的关键词，包括：
- characters: 角色的具体外貌描述（发色、服装、表情等）
- scene: 场景环境描述（室内/户外、具体地点）
- mood: 情绪氛围（严肃、教育性、安全等）
- actions: 具体动作描述（学习、遵守、保护等）
- objects: 相关物品（标志、规则牌、安全设备等）
- style: 统一使用"温暖儿童插画风格，柔和色彩"

输出格式：
{
  "title": "故事标题",
  "ageGroup": "{age_range}",
  "theme": "法律常识",
  "characters": [
    {
      "name": "角色名称",
      "description": "角色外貌描述",
      "personality": "性格特点"
    }
  ],
  "pages": [
    {
      "id": 1,
      "content": "第1页内容...",
      "isInteractive": false,
      "image_keywords": {
        "characters": ["角色1外貌", "角色2外貌"],
        "scene": "场景描述",
        "mood": "情绪氛围",
        "actions": ["动作1", "动作2"],
        "objects": ["物品1", "物品2"],
        "style": "温暖儿童插画风格，柔和色彩"
      }
    },
    {
      "id": 4,
      "content": "第4页内容...",
      "isInteractive": true,
      "interactiveQuestion": "问题内容...",
      "guidancePrompt": "引导提示内容...",
      "image_keywords": {
        "characters": ["角色1外貌", "角色2外貌"],
        "scene": "场景描述",
        "mood": "情绪氛围",
        "actions": ["动作1", "动作2"],
        "objects": ["物品1", "物品2"],
        "style": "温暖儿童插画风格，柔和色彩"
      }
    }
    // 其他页面...
  ]
}
`,

  "人伦道德": `
请为{age_range}岁的自闭症儿童创作一个关于"人伦道德"的12页绘本故事。

故事主题要求：
- 教导基本的道德品质：诚实、善良、勇敢、感恩
- 展现如何尊重长辈和关爱他人
- 包含助人为乐和无私奉献的情节
- 教导如何做出正确的道德选择
- 强调同情心和责任感的培养

角色设定建议：
- 主角：一个有爱心的小动物
- 配角：需要帮助的朋友、长辈、陌生人等
- 情境：社区、学校、家庭等各种需要道德选择的场景

具体要求：
1. 故事应该简单、清晰，使用具体而非抽象的语言
2. 每页内容控制在100-150字左右
3. 使用简单的句子结构和明确的因果关系
4. 避免使用隐喻、反语或复杂的修辞手法
5. 故事中应包含明确的道德教育和品格培养
6. 在第4页、第8页和第11页设计交互环节
7. 交互问题必须专门针对自闭症儿童的病症特征设计，包括：
   - 社交沟通困难：设计渐进式社交技能训练问题
   - 感觉处理差异：考虑感官敏感性，避免过度刺激的情境
   - 重复行为和特殊兴趣：利用这些特点设计吸引注意的问题
   - 变化适应困难：提供结构化、可预测的问题框架
   - 抽象思维挑战：使用具体、视觉化的问题描述
8. 每个交互问题都需要配备一个引导提示，使用分步骤的引导方式
9. 交互问题设计原则：
   - 第4页：道德认知启蒙（理解善恶概念、培养同理心）
   - 第8页：道德选择技能（面临道德冲突时的决策能力）
   - 第11页：道德行为实践（将道德理念转化为具体行动）
10. 问题复杂度要求：
    - 包含多个子问题，训练序列思维
    - 要求因果关系分析，提升逻辑推理
    - 鼓励个人经历分享，促进自我表达
    - 提供具体情境，避免抽象概念

图像关键词要求：
为每页生成适合图像生成的关键词，包括：
- characters: 角色的具体外貌描述（发色、服装、表情等）
- scene: 场景环境描述（室内/户外、具体地点）
- mood: 情绪氛围（善良、温暖、正义等）
- actions: 具体动作描述（帮助、关爱、分享等）
- objects: 相关物品（礼物、工具、需要帮助的物品等）
- style: 统一使用"温暖儿童插画风格，柔和色彩"

输出格式：
{
  "title": "故事标题",
  "ageGroup": "{age_range}",
  "theme": "人伦道德",
  "characters": [
    {
      "name": "角色名称",
      "description": "角色外貌描述",
      "personality": "性格特点"
    }
  ],
  "pages": [
    {
      "id": 1,
      "content": "第1页内容...",
      "isInteractive": false,
      "image_keywords": {
        "characters": ["角色1外貌", "角色2外貌"],
        "scene": "场景描述",
        "mood": "情绪氛围",
        "actions": ["动作1", "动作2"],
        "objects": ["物品1", "物品2"],
        "style": "温暖儿童插画风格，柔和色彩"
      }
    },
    {
      "id": 4,
      "content": "第4页内容...",
      "isInteractive": true,
      "interactiveQuestion": "问题内容...",
      "guidancePrompt": "引导提示内容...",
      "image_keywords": {
        "characters": ["角色1外貌", "角色2外貌"],
        "scene": "场景描述",
        "mood": "情绪氛围",
        "actions": ["动作1", "动作2"],
        "objects": ["物品1", "物品2"],
        "style": "温暖儿童插画风格，柔和色彩"
      }
    }
    // 其他页面...
  ]
}
`
};

// 图片生成提示词模板
const IMAGE_PROMPT_TEMPLATE = `
为自闭症儿童绘本创作一张插图，描述以下场景：

"{scene_description}"

要求：
1. 使用明亮、柔和的色彩
2. 简洁清晰的线条和形状
3. 避免过于复杂或混乱的背景
4. 角色表情要明确、易于识别
5. 图像应具有温暖、友好的氛围
6. 适合{age_range}岁自闭症儿童的视觉感知特点
7. 风格应保持一致，类似儿童插画书
8. 避免使用过多的文字或抽象符号

图像应该能够直观地表达场景内容，帮助自闭症儿童理解故事情节。
`;

// 专为LiblibAI image2image功能设计的提示词模板（优化版）
const IMAGE2IMAGE_PROMPT_TEMPLATE = `
Create a cheerful children's storybook illustration using the reference image style:

Scene: {scene_description}

Style Match:
- Same artistic style as reference
- Same watercolor technique
- Same color palette
- Same line style
- Same character design
- Same lighting style

Content Update:
- Update scene based on: {user_answer}
- Add new elements in same style
- Show happy character expressions
- Age-appropriate for {age_range} years old
- Family-friendly content

Create a wholesome children's book illustration that matches the reference style perfectly.
`;

// 交互式插画生成提示词模板（优化版，避免敏感内容检测）
const INTERACTIVE_ILLUSTRATION_PROMPT_TEMPLATE = `
Create a cheerful children's book illustration based on this story response:

Story Response: "{user_answer}"
Context: "{story_context}"
Page: {page_number}

Main Characters:
- Bear Bobo: Brown bear, round face, happy expression, red shirt, blue pants
- Rabbit Lily: Gray rabbit, long ears, kind expression, pink dress
- Turtle Teacher: Green turtle, brown shell, glasses, friendly look
- Squirrel Friends: Brown squirrels, fluffy tails, colorful clothes

Art Style:
1. Bright cheerful children's book style
2. Soft pastel colors
3. Simple clean shapes
4. Smooth watercolor look
5. Clean simple background
6. Happy character expressions
7. Welcoming scene design
8. Elements from the story response

Create a wholesome family-friendly illustration that matches children's storybook art style.
`;

// 交互问题生成提示词模板
const INTERACTIVE_QUESTION_PROMPT_TEMPLATE = `
为{age_range}岁自闭症儿童设计一个关于"{context}"的交互问题。

故事背景：
"{story_context}"

要求：
1. 问题应促进语言表达，而非简单的是/否回答
2. 问题应与故事情节和主题"{theme}"相关
3. 问题应考虑自闭症儿童的认知特点
4. 问题应鼓励分享个人经历或想法
5. 问题应有明确的焦点，避免模糊或抽象
6. 同时设计一个引导提示，用于在儿童30秒内没有回答时提供帮助

输出格式：
{
  "question": "问题内容...",
  "guidance_prompt": "引导提示内容..."
}
`;

// 专门针对自闭症儿童的性能评估提示词模板
const AUTISM_PERFORMANCE_ANALYSIS_PROMPT_TEMPLATE = `
你是一位专业的自闭症儿童康复专家和语言治疗师，请分析以下6-8岁自闭症儿童在交互式绘本中的回答表现。

背景信息：
- 绘本主题：友谊与社交技能
- 目标年龄：6-8岁自闭症儿童
- 交互环节：3个渐进式社交情境问题

交互问题与回答：

问题1（社交启动技能）："{question1}"
儿童回答1："{answer1}"

问题2（群体融入技能）："{question2}"
儿童回答2："{answer2}"

问题3（友谊理解与反思）："{question3}"
儿童回答3："{answer3}"

请从以下专业维度进行深度分析（满分5分）：

1. 语言表达能力（Language Expression）
   - 词汇使用的丰富度和准确性
   - 句式结构的复杂程度
   - 语言组织的逻辑性
   - 表达的清晰度和完整性

2. 社交认知能力（Social Cognition）
   - 对社交情境的理解程度
   - 他人情感和意图的识别能力
   - 社交规则和礼仪的掌握
   - 换位思考和共情能力

3. 逻辑推理能力（Logical Reasoning）
   - 因果关系的理解和表达
   - 问题解决的策略性思维
   - 序列性思维和步骤规划
   - 抽象概念的理解能力

4. 情感调节能力（Emotional Regulation）
   - 情感词汇的使用和理解
   - 情绪状态的识别和表达
   - 应对策略的提出和运用
   - 情感共鸣和情绪管理

5. 自我反思能力（Self-Reflection）
   - 对自身行为的觉察
   - 从经验中学习的能力
   - 自我改进的意识
   - 个人经历的整合表达

请特别关注自闭症儿童的特殊需求：
- 感觉处理差异
- 社交沟通挑战
- 重复行为和特殊兴趣
- 变化适应困难
- 非语言沟通理解

输出格式（严格JSON格式）：
{
  "assessment_summary": {
    "child_age_estimate": "基于回答推测的发展水平",
    "overall_performance": "整体表现概述",
    "key_strengths": ["优势1", "优势2", "优势3"],
    "areas_for_improvement": ["需改进领域1", "需改进领域2", "需改进领域3"]
  },
  "detailed_scores": {
    "language_expression": {
      "score": 分数,
      "analysis": "详细分析...",
      "evidence": ["支持证据1", "支持证据2"],
      "recommendations": ["建议1", "建议2"]
    },
    "social_cognition": {
      "score": 分数,
      "analysis": "详细分析...",
      "evidence": ["支持证据1", "支持证据2"],
      "recommendations": ["建议1", "建议2"]
    },
    "logical_reasoning": {
      "score": 分数,
      "analysis": "详细分析...",
      "evidence": ["支持证据1", "支持证据2"],
      "recommendations": ["建议1", "建议2"]
    },
    "emotional_regulation": {
      "score": 分数,
      "analysis": "详细分析...",
      "evidence": ["支持证据1", "支持证据2"],
      "recommendations": ["建议1", "建议2"]
    },
    "self_reflection": {
      "score": 分数,
      "analysis": "详细分析...",
      "evidence": ["支持证据1", "支持证据2"],
      "recommendations": ["建议1", "建议2"]
    }
  },
  "intervention_plan": {
    "immediate_goals": ["短期目标1", "短期目标2", "短期目标3"],
    "long_term_goals": ["长期目标1", "长期目标2"],
    "recommended_activities": [
      {
        "activity": "活动名称",
        "description": "活动描述",
        "target_skills": ["目标技能1", "目标技能2"],
        "frequency": "建议频率"
      }
    ],
    "parent_guidance": ["家长指导建议1", "家长指导建议2", "家长指导建议3"],
    "progress_indicators": ["进步指标1", "进步指标2"]
  },
  "professional_notes": {
    "autism_specific_observations": "自闭症特征相关观察",
    "communication_style": "沟通风格分析",
    "learning_preferences": "学习偏好分析",
    "sensory_considerations": "感觉处理相关建议"
  }
}
`;

// 保持原有的简单版本作为备用
const PERFORMANCE_ANALYSIS_PROMPT_TEMPLATE = `
分析自闭症儿童在以下交互问题中的回答表现：

问题1："{question1}"
回答1："{answer1}"

问题2："{question2}"
回答2："{answer2}"

问题3："{question3}"
回答3："{answer3}"

请从以下四个维度进行评估（满分5分）：
1. 语言词汇量：评估词汇丰富度、表达多样性
2. 思维逻辑：评估因果关系理解、逻辑推理能力
3. 社会适应：评估社交规则理解、人际互动意识
4. 情感识别：评估情感表达、共情能力

对于每个维度，请提供具体分析和改进建议。

输出格式：
{
  "scores": {
    "language_vocabulary": 分数,
    "logical_thinking": 分数,
    "social_adaptation": 分数,
    "emotional_recognition": 分数
  },
  "analysis": {
    "language_vocabulary": "分析和建议...",
    "logical_thinking": "分析和建议...",
    "social_adaptation": "分析和建议...",
    "emotional_recognition": "分析和建议..."
  },
  "overall_recommendation": "综合建议..."
}
`;

export {
  STORY_PROMPT_TEMPLATE,
  THEME_SPECIFIC_PROMPTS,
  IMAGE_PROMPT_TEMPLATE,
  IMAGE2IMAGE_PROMPT_TEMPLATE,
  INTERACTIVE_ILLUSTRATION_PROMPT_TEMPLATE,
  INTERACTIVE_QUESTION_PROMPT_TEMPLATE,
  PERFORMANCE_ANALYSIS_PROMPT_TEMPLATE,
  AUTISM_PERFORMANCE_ANALYSIS_PROMPT_TEMPLATE
};
