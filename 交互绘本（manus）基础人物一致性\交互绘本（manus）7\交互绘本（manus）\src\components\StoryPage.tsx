// 集成基于用户回答生成插画的功能到StoryPage组件

import { useState, useEffect, useRef } from 'react';
import { generateIllustrationFromAnswer } from '../services/illustrationGenerator.js';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Textarea } from './ui/textarea';
import { Alert, AlertDescription } from './ui/alert';
import { Progress } from './ui/progress';
import { Badge } from './ui/badge';
import { Skeleton } from './ui/skeleton';
import { Tooltip } from './ui/tooltip';
import i18nService, { Language } from '../services/i18nService';
import speechService from '../services/speechService';

// 获取绘本图片的函数
async function getStoryBookImages() {
  console.log('📚 获取绘本参考图片...');

  // 使用统一的参考图片URL，确保风格一致性
  const referenceImageUrl = 'https://liblibai-tmp-image.liblib.cloud/img/9dbb5de4e30a42afaff5b04e13eb518e/8e84eb22f8ddf40803db5ad75582ccb6bfe3312db9899156d3dbba31d4ccb90e.png';

  // 为所有页面提供相同的参考图像
  const images = [
    { pageIndex: 0, url: referenceImageUrl },
    { pageIndex: 1, url: referenceImageUrl },
    { pageIndex: 2, url: referenceImageUrl },
    { pageIndex: 3, url: referenceImageUrl },
    { pageIndex: 4, url: referenceImageUrl },
    { pageIndex: 5, url: referenceImageUrl },
    { pageIndex: 6, url: referenceImageUrl },
    { pageIndex: 7, url: referenceImageUrl },
    { pageIndex: 8, url: referenceImageUrl },
    { pageIndex: 9, url: referenceImageUrl },
    { pageIndex: 10, url: referenceImageUrl },
    { pageIndex: 11, url: referenceImageUrl }
  ];

  console.log(`✅ 获取到${images.length}张参考图片，全部基于统一的参考图像`);
  return images;
}



interface StoryPageProps {
  page: {
    id: number;
    content: string;
    image?: string;
    imagePath?: string;
    isInteractive: boolean;
    question?: string;
    guidance?: string;
    interactiveQuestion?: string;
    guidancePrompt?: string;
  };
  onNext: () => void;
  onResponseSubmit: (pageId: number, response: string) => void;
  userResponses?: Array<{pageId: number, response: string}>;
  storyData?: any; // 新增：故事数据
}

export function StoryPage({
  page,
  onNext,
  onResponseSubmit,
  userResponses = [],
  storyData
}: StoryPageProps) {
  const [userResponse, setUserResponse] = useState('');
  const [responseSubmitted, setResponseSubmitted] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [readingComplete, setReadingComplete] = useState(false);
  const [timeLeft, setTimeLeft] = useState(30);
  const [showGuidance, setShowGuidance] = useState(false);
  const timerStartedRef = useRef(false);
  const timerRef = useRef<number | null>(null);
  const [generatingImage, setGeneratingImage] = useState(false);
  const [generatedImageUrl, setGeneratedImageUrl] = useState('');
  const [imageError, setImageError] = useState('');
  const [currentLanguage, setCurrentLanguage] = useState<Language>(i18nService.getCurrentLanguage());

  // 监听语言变化
  useEffect(() => {
    const handleLanguageChange = (language: Language) => {
      setCurrentLanguage(language);
      speechService.setLanguage(language);
    };

    i18nService.addLanguageChangeListener(handleLanguageChange);

    return () => {
      i18nService.removeLanguageChangeListener(handleLanguageChange);
    };
  }, []);

  // 检查是否已有该页面的回答
  useEffect(() => {
    if (page.isInteractive && userResponses) {
      const existingResponse = userResponses.find(r => r.pageId === page.id);
      if (existingResponse) {
        setUserResponse(existingResponse.response);
        setResponseSubmitted(true);
      } else {
        setUserResponse('');
        setResponseSubmitted(false);
      }
    }
  }, [page.id, userResponses]);

  // 重置状态
  useEffect(() => {
    setTimeLeft(30);
    setShowGuidance(false);
    timerStartedRef.current = false;
    setGeneratedImageUrl('');
    setImageError('');

    // 自动朗读页面内容
    readPageContent();

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }

      // 确保在组件卸载时停止语音
      if ('speechSynthesis' in window) {
        window.speechSynthesis.cancel();
      }
    };
  }, [page.id]);

  // 监听朗读完成后开始计时（仅交互页面）
  useEffect(() => {
    if (page.isInteractive && readingComplete && !responseSubmitted && !timerStartedRef.current) {
      timerStartedRef.current = true;

      timerRef.current = window.setInterval(() => {
        setTimeLeft(prevTime => {
          if (prevTime <= 1) {
            if (timerRef.current) {
              clearInterval(timerRef.current);
            }
            setShowGuidance(true);
            // 朗读引导提示
            const guidanceText = page.guidance || page.guidancePrompt;
            if (guidanceText) {
              speakText(guidanceText);
            }
            return 0;
          }
          return prevTime - 1;
        });
      }, 1000);
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [readingComplete, page.isInteractive, responseSubmitted, page.guidance]);

  // 朗读页面内容
  const readPageContent = () => {
    setIsSpeaking(true);

    const textToRead = page.isInteractive ?
      (page.question || page.interactiveQuestion || '') : page.content;

    speechService.speak(textToRead, {
      onStart: () => setIsSpeaking(true),
      onEnd: () => {
        setIsSpeaking(false);
        setReadingComplete(true);
      },
      onError: (error) => {
        console.error('朗读错误:', error);
        setIsSpeaking(false);
      }
    });
  };

  // 朗读文本
  const speakText = (text: string) => {
    speechService.speak(text);
  };

  // 开始语音输入
  const startVoiceInput = () => {
    speechService.startSpeechRecognition({
      onResult: (transcript) => {
        setUserResponse(transcript);
      },
      onStart: () => {
        setIsListening(true);
      },
      onEnd: () => {
        setIsListening(false);
      },
      onError: (error) => {
        console.error('语音识别错误:', error);
        setIsListening(false);
        alert(i18nService.t('browserNotSupported'));
      }
    });
  };

  // 提交回答
  const handleSubmit = async () => {
    if (userResponse.trim() === '') return;

    // 停止计时器
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    // 提交回答
    onResponseSubmit(page.id, userResponse);
    setResponseSubmitted(true);

    // 朗读感谢信息
    speakText(i18nService.t('thankYou'));

    // 生成基于回答的插画
    try {
      setGeneratingImage(true);
      setImageError('');

      // 获取所有可用的绘本图像
      // 根据分析报告的策略：使用前后页面的插画作为参考图像
      // 使用绘本的实际图片URL（通过公开可访问的CDN或图床服务）
      const allImages = await getStoryBookImages();

      console.log('🖼️ 参考图像列表:', allImages);

      // 当前页面索引
      const currentPageIndex = page.id - 1;

      // 当前故事上下文
      const context = {
        currentPageIndex,
        currentPage: page
      };

      // 生成插画（传入故事数据）
      const imageUrl = await generateIllustrationFromAnswer(
        userResponse,
        page.id,
        context,
        allImages,
        storyData
      );

      setGeneratedImageUrl(imageUrl);
    } catch (error) {
      console.error('生成插画失败:', error);
      setImageError(`生成插画失败: ${(error as Error).message || '未知错误'}`);
    } finally {
      setGeneratingImage(false);
    }
  };

  // 重新生成插画
  const handleRegenerateImage = async () => {
    try {
      setGeneratingImage(true);
      setImageError('');

      // 获取所有可用的绘本图像
      // 根据分析报告的策略：使用前后页面的插画作为参考图像
      // 使用绘本的实际图片URL（通过公开可访问的CDN或图床服务）
      const allImages = await getStoryBookImages();

      // 当前页面索引
      const currentPageIndex = page.id - 1;

      // 当前故事上下文
      const context = {
        currentPageIndex,
        currentPage: page
      };

      // 生成插画（传入故事数据）
      const imageUrl = await generateIllustrationFromAnswer(
        userResponse,
        page.id,
        context,
        allImages,
        storyData
      );

      setGeneratedImageUrl(imageUrl);
    } catch (error) {
      console.error('重新生成插画失败:', error);
      setImageError(`重新生成插画失败: ${(error as Error).message || '未知错误'}`);
    } finally {
      setGeneratingImage(false);
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>
            {page.isInteractive ? `🎯 ${i18nService.t('interactive')}` : `📖 ${i18nService.t('page')} ${page.id}`}
          </CardTitle>
          <div className="flex gap-2">
            {page.isInteractive && (
              <Badge variant="outline">
                {responseSubmitted ? `✅ ${i18nService.t('completed')}` : `⏳ ${i18nService.t('pending')}`}
              </Badge>
            )}
            {isSpeaking && (
              <Badge variant="secondary">
                {i18nService.t('reading')}
              </Badge>
            )}
            {generatingImage && (
              <Badge variant="default">
                {i18nService.t('generating')}
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent>

        {/* 页面图片 */}
        <div className="mb-4 relative">
          {page.isInteractive && responseSubmitted && generatedImageUrl ? (
            // 显示基于用户回答生成的插画
            <div className="relative">
              <img
                src={generatedImageUrl}
                alt={`基于回答生成的插画`}
                className="w-full h-auto rounded-lg mb-2"
              />
              <div className="text-sm text-gray-500 italic text-center">
                {currentLanguage === 'zh' ? '基于你的回答生成的插画' : 'Illustration generated based on your answer'}
              </div>

              {/* 重新生成按钮 */}
              <Button
                onClick={handleRegenerateImage}
                disabled={generatingImage}
                variant="outline"
                size="sm"
                className="mt-2"
              >
                {generatingImage ? i18nService.t('generating') : i18nService.t('regenerateImage')}
              </Button>
            </div>
          ) : (
            // 显示原始页面图片
            (page.image || page.imagePath) && (
              <img
                src={page.image || page.imagePath}
                alt={`第 ${page.id} 页插图`}
                className="w-full h-auto rounded-lg"
              />
            )
          )}

          {/* 图片生成错误提示 */}
          {imageError && (
            <Alert variant="destructive" className="mt-2">
              <AlertDescription>
                {imageError}
              </AlertDescription>
            </Alert>
          )}

          {/* 图片生成加载状态 */}
          {generatingImage && (
            <div className="absolute inset-0 flex items-center justify-center bg-white bg-opacity-90 rounded-lg">
              <div className="flex flex-col items-center space-y-4">
                <Skeleton className="h-8 w-8 rounded-full" />
                <div className="text-center">
                  <div className="text-blue-600 font-medium mb-2">{i18nService.t('generatingImage')}</div>
                  <div className="text-sm text-gray-500">
                    {currentLanguage === 'zh' ? '根据你的回答创作专属图片' : 'Creating exclusive images based on your answers'}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* 页面文本内容 */}
        <div className="prose max-w-none">
          {page.isInteractive ? (
            <div>
              <p className="font-medium text-lg mb-4">
                {page.question || page.interactiveQuestion}
              </p>

              {/* 显示引导提示 */}
              {showGuidance && !responseSubmitted && (
                <Alert variant="warning" className="mb-4">
                  <AlertDescription>
                    💡 {page.guidance || page.guidancePrompt}
                  </AlertDescription>
                </Alert>
              )}

              {/* 显示用户回答 */}
              {responseSubmitted ? (
                <div className="mt-4">
                  <h3 className="font-medium text-gray-700">{i18nService.t('yourAnswer')}</h3>
                  <p className="p-3 bg-blue-50 rounded-md">{userResponse}</p>
                </div>
              ) : (
                <div className="mt-4">
                  {/* 倒计时 */}
                  <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                    <div className="flex justify-between text-sm text-gray-600 mb-2">
                      <span>{i18nService.t('timeRemaining')}</span>
                      <Badge variant={timeLeft <= 10 ? 'destructive' : 'outline'}>
                        {timeLeft}{i18nService.t('seconds')}
                      </Badge>
                    </div>
                    <Progress
                      value={(timeLeft / 30) * 100}
                      className="h-2"
                    />
                  </div>

                  {/* 回答输入框 */}
                  <Textarea
                    value={userResponse}
                    onChange={(e) => setUserResponse(e.target.value)}
                    placeholder={i18nService.t('pleaseAnswer')}
                    rows={4}
                    className="mb-3"
                  />

                  <div className="flex gap-2">
                    {/* 语音输入按钮 */}
                    <Tooltip content={currentLanguage === 'zh' ? '点击开始语音输入，说出你的回答' : 'Click to start voice input and speak your answer'}>
                      <Button
                        onClick={startVoiceInput}
                        variant="outline"
                        className="flex items-center gap-2"
                      >
                        {isListening ? (
                          <>
                            <span>{i18nService.t('listening')}</span>
                            <span className="flex h-3 w-3">
                              <span className="animate-ping absolute h-3 w-3 rounded-full bg-green-400 opacity-75"></span>
                              <span className="relative rounded-full h-3 w-3 bg-green-500"></span>
                            </span>
                          </>
                        ) : (
                          <>
                            {i18nService.t('voiceInput')}
                          </>
                        )}
                      </Button>
                    </Tooltip>

                    {/* 提交按钮 */}
                    <Tooltip content={currentLanguage === 'zh' ? '提交你的回答并生成个性化插画' : 'Submit your answer and generate personalized illustration'}>
                      <Button
                        onClick={handleSubmit}
                        disabled={userResponse.trim() === ''}
                      >
                        {i18nService.t('submitAnswer')}
                      </Button>
                    </Tooltip>
                  </div>
                </div>
              )}
            </div>
          ) : (
            <p>{page.content}</p>
          )}
        </div>

        {/* 朗读按钮 */}
        <div className="mb-4">
          <Tooltip content={currentLanguage === 'zh' ? '点击朗读当前页面内容' : 'Click to read current page content'}>
            <Button
              onClick={readPageContent}
              disabled={isSpeaking}
              variant="outline"
              className="mr-2"
            >
              {isSpeaking ? i18nService.t('reading') : i18nService.t('readContent')}
            </Button>
          </Tooltip>
        </div>

        {/* 继续阅读按钮（非交互页面） */}
        {!page.isInteractive && (
          <Button
            onClick={onNext}
            disabled={!readingComplete}
          >
            {!readingComplete ? i18nService.t('waitForReading') : i18nService.t('continueReading')}
          </Button>
        )}

        {/* 继续阅读按钮（交互页面，已回答） */}
        {page.isInteractive && responseSubmitted && (
          <Button onClick={onNext}>
            {i18nService.t('continueReading')}
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
