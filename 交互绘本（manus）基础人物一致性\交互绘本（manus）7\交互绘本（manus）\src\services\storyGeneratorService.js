// 故事生成服务
// 使用OpenAI API为自闭症儿童生成针对不同主题的绘本故事

import apiKeyManager from './apiKeyManager.js';
import { THEME_SPECIFIC_PROMPTS } from './promptTemplates.js';

class StoryGeneratorService {
  constructor() {
    this.availableThemes = [
      '人际关系',
      '家庭生活', 
      '法律常识',
      '人伦道德'
    ];
    this.defaultAgeRange = '6-8岁';
  }

  // 获取可用的主题列表
  getAvailableThemes() {
    return this.availableThemes;
  }

  // 生成指定主题的故事
  async generateStory(theme, ageRange = this.defaultAgeRange) {
    try {
      // 验证主题是否支持
      if (!this.availableThemes.includes(theme)) {
        throw new Error(`不支持的主题: ${theme}。支持的主题: ${this.availableThemes.join(', ')}`);
      }

      // 检查OpenAI API是否已初始化
      if (!apiKeyManager.isOpenAIInitialized()) {
        throw new Error('OpenAI API密钥未初始化，请先设置API密钥');
      }

      console.log(`🎨 开始生成主题为"${theme}"的故事...`);

      // 获取对应主题的提示词模板
      const promptTemplate = THEME_SPECIFIC_PROMPTS[theme];
      if (!promptTemplate) {
        throw new Error(`未找到主题"${theme}"的提示词模板`);
      }

      // 替换模板中的变量
      const prompt = promptTemplate.replace(/{age_range}/g, ageRange);

      console.log('📝 发送请求到OpenAI API...');

      // 调用OpenAI API生成故事
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKeyManager.getApiKey()}`
        },
        body: JSON.stringify({
          model: 'gpt-4o',
          messages: [
            {
              role: 'system',
              content: '你是一位专业的儿童绘本作家，擅长为自闭症儿童创作教育性故事。请严格按照JSON格式输出，确保包含characters字段和每页的image_keywords字段。生成的故事必须适合自闭症儿童的认知特点。'
            },
            { role: 'user', content: prompt }
          ],
          temperature: 0.7,
          max_tokens: 3000
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(`OpenAI API错误: ${error.error?.message || '未知错误'}`);
      }

      const data = await response.json();
      const storyContent = data.choices[0].message.content;

      console.log('✅ 收到OpenAI响应，开始解析故事内容...');
      console.log('📄 OpenAI原始响应内容:', storyContent.substring(0, 500) + '...');

      // 解析JSON格式的故事内容
      let storyData;
      try {
        // 尝试提取JSON内容（可能包含在代码块中）
        const jsonMatch = storyContent.match(/```json\s*([\s\S]*?)\s*```/) ||
                         storyContent.match(/\{[\s\S]*\}/);

        if (jsonMatch) {
          storyData = JSON.parse(jsonMatch[1] || jsonMatch[0]);
        } else {
          storyData = JSON.parse(storyContent);
        }

        console.log('📊 解析后的故事数据结构:', {
          title: storyData.title,
          hasCharacters: !!storyData.characters,
          charactersCount: storyData.characters?.length || 0,
          pagesCount: storyData.pages?.length || 0,
          firstPageHasKeywords: !!storyData.pages?.[0]?.image_keywords
        });

      } catch (parseError) {
        console.error('JSON解析失败:', parseError);
        console.log('原始内容:', storyContent);
        throw new Error('故事内容解析失败，请重试');
      }

      // 验证故事数据结构
      const validatedStory = this.validateAndFormatStory(storyData, theme, ageRange);

      console.log('🎉 故事生成成功!');
      return validatedStory;

    } catch (error) {
      console.error('❌ 故事生成失败:', error);
      throw error;
    }
  }

  // 验证和格式化故事数据
  validateAndFormatStory(storyData, theme, ageRange) {
    // 基本结构验证
    if (!storyData.title || !storyData.pages || !Array.isArray(storyData.pages)) {
      throw new Error('故事数据结构不完整');
    }

    // 确保有12页
    if (storyData.pages.length !== 12) {
      throw new Error(`故事应该有12页，但收到了${storyData.pages.length}页`);
    }

    // 验证交互页面
    const interactivePages = storyData.pages.filter(page => page.isInteractive);
    if (interactivePages.length !== 3) {
      console.warn(`期望3个交互页面，但找到了${interactivePages.length}个`);
    }

    // 格式化故事数据以匹配现有的数据结构
    const formattedStory = {
      title: storyData.title,
      ageGroup: ageRange,
      theme: theme,
      characters: storyData.characters || [], // 保留OpenAI生成的角色信息
      pages: storyData.pages.map((page, index) => ({
        id: index + 1,
        content: page.content,
        isInteractive: page.isInteractive || false,
        interactiveQuestion: page.interactiveQuestion || page.interactive_question,
        guidancePrompt: page.guidancePrompt || page.guidance_prompt,
        imagePath: page.imagePath || `/assets/images/page${index + 1}.png`,
        image_keywords: page.image_keywords || null // 保留OpenAI生成的图像关键词
      }))
    };

    // 调试日志：检查OpenAI返回的数据
    console.log('🔍 OpenAI返回的角色信息:', storyData.characters);
    console.log('🔍 第一页的图像关键词:', storyData.pages[0]?.image_keywords);

    // 确保第4、8、11页是交互页面
    [4, 8, 11].forEach(pageNum => {
      const page = formattedStory.pages[pageNum - 1];
      if (page && !page.isInteractive) {
        console.warn(`第${pageNum}页应该是交互页面，但未标记为交互`);
      }
    });

    return formattedStory;
  }

  // 获取主题描述
  getThemeDescription(theme) {
    const descriptions = {
      '人际关系': '学习如何与他人建立友谊，培养社交技能和沟通能力',
      '家庭生活': '了解家庭的温暖，学习家庭责任和表达对家人的爱',
      '法律常识': '用简单的方式学习基本规则，培养安全意识和守法观念',
      '人伦道德': '培养诚实、善良等品德，学习关爱他人和做正确的选择'
    };
    return descriptions[theme] || '探索生活中的重要主题';
  }

  // 检查服务是否可用
  isServiceAvailable() {
    return apiKeyManager.isOpenAIInitialized();
  }

  // 初始化OpenAI API密钥
  initializeApiKey(apiKey) {
    apiKeyManager.initialize(apiKey);
  }

  // 清除API密钥
  clearApiKey() {
    apiKeyManager.clearOpenAI();
  }
}

// 创建单例实例
const storyGeneratorService = new StoryGeneratorService();

export default storyGeneratorService;
