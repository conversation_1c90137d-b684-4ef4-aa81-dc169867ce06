// 故事插图生成服务
// 为新生成的故事自动生成插图
// 增强版：集成风格一致性策略

import liblibService from './liblibService.js';
import enhancedIllustrationService from './enhancedIllustrationService.js';

class StoryIllustrationGenerator {
  constructor() {
    this.isGenerating = false;
    this.generationQueue = [];
  }

  // 为整个故事生成插图（增强版）
  async generateStoryIllustrations(storyData, onProgress = null) {
    if (this.isGenerating) {
      throw new Error('正在生成其他故事的插图，请稍候');
    }

    this.isGenerating = true;
    const generatedImages = {};

    try {
      console.log(`🎨 使用增强版风格一致性策略为故事《${storyData.title}》生成插图...`);

      // 设置故事上下文到增强服务
      enhancedIllustrationService.setStoryContext(storyData);

      // 只为非交互页面生成插图（交互页面会根据用户回答动态生成）
      const nonInteractivePages = storyData.pages.filter(page => !page.isInteractive);

      console.log(`📄 需要生成插图的页面数量: ${nonInteractivePages.length}`);
      console.log(`🎯 使用增强风格一致性策略确保视觉连贯性`);

      for (let i = 0; i < nonInteractivePages.length; i++) {
        const page = nonInteractivePages[i];

        try {
          console.log(`🖼️ 正在生成第${page.id}页插图（增强版）...`);

          // 调用进度回调
          if (onProgress) {
            onProgress({
              current: i + 1,
              total: nonInteractivePages.length,
              currentPage: page.id,
              status: 'generating'
            });
          }

          // 使用增强版生成插图
          const imageUrl = await this.generatePageIllustrationEnhanced(page, storyData, i);
          generatedImages[page.id] = imageUrl;

          console.log(`✅ 第${page.id}页插图生成成功（增强版）: ${imageUrl}`);

          // 调用进度回调
          if (onProgress) {
            onProgress({
              current: i + 1,
              total: nonInteractivePages.length,
              currentPage: page.id,
              status: 'completed',
              imageUrl: imageUrl
            });
          }

          // 避免API调用过于频繁，添加延迟
          if (i < nonInteractivePages.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
          
        } catch (error) {
          console.error(`❌ 第${page.id}页插图生成失败:`, error);
          
          // 调用进度回调
          if (onProgress) {
            onProgress({
              current: i + 1,
              total: nonInteractivePages.length,
              currentPage: page.id,
              status: 'error',
              error: error.message
            });
          }
          
          // 继续生成其他页面，不因单页失败而停止
          continue;
        }
      }
      
      console.log(`🎉 故事插图生成完成! 成功生成 ${Object.keys(generatedImages).length} 张插图`);
      return generatedImages;
      
    } catch (error) {
      console.error('❌ 故事插图生成失败:', error);
      throw error;
    } finally {
      this.isGenerating = false;
    }
  }

  // 为单个页面生成插图（增强版）
  async generatePageIllustrationEnhanced(page, storyData, pageIndex) {
    try {
      console.log(`🎨 使用增强版策略生成第${page.id}页插图...`);

      // 检查LiblibAI服务是否可用
      if (!liblibService.isApiKeyInitialized()) {
        throw new Error('LiblibAI服务未初始化，无法生成插图');
      }

      // 分析页面内容，提取角色和场景信息
      const pageAnalysis = this.analyzePageContent(page, storyData);

      // 构建页面信息对象
      const pageInfo = {
        pageNumber: page.id,
        content: page.content,
        sceneType: pageAnalysis.sceneType,
        characters: pageAnalysis.characters,
        isInteractive: false,
        actions: pageAnalysis.actions,
        uniqueElements: pageAnalysis.uniqueElements, // 新增
        pageSpecificContent: pageAnalysis.pageSpecificContent, // 新增
        storyData: storyData // 新增：传递完整的故事数据
      };

      console.log(`🔧 页面分析结果:`, pageAnalysis);

      // 使用增强插画服务生成插图
      const imageUrl = await enhancedIllustrationService.generatePageIllustration(pageInfo);

      console.log(`✅ 增强版插图生成完成: ${imageUrl}`);
      return imageUrl;

    } catch (error) {
      console.error(`❌ 增强版插图生成失败，回退到原始方法:`, error);

      // 回退到原始方法
      return await this.generatePageIllustration(page, storyData);
    }
  }

  // 为单个页面生成插图（原始方法，作为回退）
  async generatePageIllustration(page, storyData) {
    // 检查LiblibAI服务是否可用
    if (!liblibService.isApiKeyInitialized()) {
      throw new Error('LiblibAI服务未初始化，无法生成插图');
    }

    // 构建插图生成提示词
    const prompt = this.buildIllustrationPrompt(page, storyData);

    console.log(`📝 原始插图提示词: ${prompt}`);

    try {
      // 调用LiblibAI生成图片
      const imageUrl = await liblibService.generateImage(prompt, storyData.ageGroup || '6-8岁');

      return imageUrl;

    } catch (error) {
      console.error('LiblibAI图片生成失败:', error);
      throw new Error(`插图生成失败: ${error.message}`);
    }
  }

  // 分析页面内容，提取关键信息（增强版）
  analyzePageContent(page, storyData) {
    const content = page.content || '';
    console.log(`🔍 分析第${page.id}页内容: "${content.substring(0, 100)}..."`);

    // 提取角色信息（智能识别）
    const characters = [];

    // 从故事数据中获取真实角色信息
    const storyCharacters = storyData.characters || [];
    console.log(`📚 故事角色列表:`, storyCharacters);

    // 优先从故事角色列表中匹配
    storyCharacters.forEach(storyChar => {
      const charName = typeof storyChar === 'string' ? storyChar : storyChar.name;
      if (content.includes(charName) || content.includes(charName.replace('小', ''))) {
        characters.push({
          name: charName,
          emotion: this.extractEmotion(content),
          action: this.extractAction(content)
        });
        console.log(`👤 检测到故事角色: ${charName}`);
      }
    });

    // 如果故事角色列表中没有匹配，尝试通用角色名称
    if (characters.length === 0) {
      const commonNames = ['小乐', '小明', '小红', '小华', '小丽', '小强', '小美'];
      commonNames.forEach(name => {
        if (content.includes(name)) {
          characters.push({
            name: name,
            emotion: this.extractEmotion(content),
            action: this.extractAction(content)
          });
          console.log(`👤 检测到通用角色: ${name}`);
        }
      });
    }

    // 如果仍然没有检测到角色，从故事数据中使用主角
    if (characters.length === 0 && storyCharacters.length > 0) {
      const mainCharacter = storyCharacters[0];
      const charName = typeof mainCharacter === 'string' ? mainCharacter : mainCharacter.name;
      characters.push({
        name: charName,
        emotion: 'neutral',
        action: 'standing'
      });
      console.log(`👤 使用故事主角: ${charName}`);
    }

    // 最后的回退选项
    if (characters.length === 0) {
      characters.push({
        name: '主角',
        emotion: 'neutral',
        action: 'standing'
      });
      console.log(`👤 使用通用主角`);
    }

    // 提取场景类型（更详细的分析）
    const sceneType = this.extractSceneType(content);
    console.log(`🎭 场景类型: ${sceneType}`);

    // 提取动作（更精确）
    const actions = this.extractActions(content);
    console.log(`🎬 动作: ${actions}`);

    // 提取页面特有的关键词
    const uniqueElements = this.extractUniqueElements(content, page.id);
    console.log(`✨ 页面特有元素: ${uniqueElements.join(', ')}`);

    return {
      characters,
      sceneType,
      actions,
      mood: this.extractMood(content),
      uniqueElements, // 新增：页面特有元素
      pageSpecificContent: this.extractPageSpecificContent(content) // 新增：页面特定内容
    };
  }

  // 提取情绪
  extractEmotion(content) {
    const emotionKeywords = {
      happy: ['开心', '高兴', '快乐', '微笑', '笑'],
      sad: ['难过', '伤心', '哭'],
      excited: ['兴奋', '激动', '期待'],
      curious: ['好奇', '疑问', '想知道'],
      shy: ['害羞', '紧张', '不好意思'],
      surprised: ['惊讶', '意外', '没想到']
    };

    for (const [emotion, keywords] of Object.entries(emotionKeywords)) {
      if (keywords.some(keyword => content.includes(keyword))) {
        return emotion;
      }
    }

    return 'neutral';
  }

  // 提取动作
  extractAction(content) {
    const actionKeywords = {
      playing: ['玩', '游戏'],
      walking: ['走', '散步'],
      talking: ['说', '聊天', '交谈'],
      reading: ['读', '看书'],
      eating: ['吃', '用餐'],
      sleeping: ['睡', '休息'],
      running: ['跑', '奔跑'],
      sitting: ['坐', '坐下']
    };

    for (const [action, keywords] of Object.entries(actionKeywords)) {
      if (keywords.some(keyword => content.includes(keyword))) {
        return action;
      }
    }

    return 'standing';
  }

  // 提取场景类型
  extractSceneType(content) {
    const sceneKeywords = {
      forest: ['森林', '树', '大树', '树林'],
      home: ['家', '房子', '屋子'],
      playground: ['操场', '游乐场', '公园'],
      indoor: ['室内', '房间', '教室'],
      garden: ['花园', '草地', '花']
    };

    for (const [scene, keywords] of Object.entries(sceneKeywords)) {
      if (keywords.some(keyword => content.includes(keyword))) {
        return scene;
      }
    }

    return 'forest'; // 默认森林场景
  }

  // 提取动作描述
  extractActions(content) {
    const actions = [];
    const actionPatterns = [
      '一起玩', '互相帮助', '分享', '交朋友', '学习', '探索', '发现',
      '走路', '跑步', '坐下', '站立', '唱歌', '跳舞', '画画', '读书',
      '吃饭', '睡觉', '思考', '微笑', '拥抱', '握手', '挥手'
    ];

    actionPatterns.forEach(action => {
      if (content.includes(action)) {
        actions.push(action);
      }
    });

    return actions.length > 0 ? actions.join('和') : '友好互动';
  }

  // 提取页面特有元素
  extractUniqueElements(content, pageId) {
    const uniqueElements = [];

    // 物品和道具
    const items = ['书', '玩具', '花', '树', '房子', '桥', '河', '山', '云', '太阳', '月亮', '星星', '球', '积木', '画笔', '音乐', '食物', '水果'];
    items.forEach(item => {
      if (content.includes(item)) {
        uniqueElements.push(item);
      }
    });

    // 时间和天气
    const timeWeather = ['早上', '中午', '下午', '晚上', '晴天', '雨天', '雪天', '春天', '夏天', '秋天', '冬天'];
    timeWeather.forEach(tw => {
      if (content.includes(tw)) {
        uniqueElements.push(tw);
      }
    });

    // 地点和环境
    const locations = ['家里', '学校', '公园', '森林', '河边', '山上', '花园', '操场', '教室', '图书馆'];
    locations.forEach(loc => {
      if (content.includes(loc)) {
        uniqueElements.push(loc);
      }
    });

    // 为每个页面添加唯一标识符，确保不同页面生成不同图片
    uniqueElements.push(`page${pageId}`);

    return uniqueElements;
  }

  // 提取页面特定内容
  extractPageSpecificContent(content) {
    // 提取关键句子或短语
    const sentences = content.split(/[。！？]/).filter(s => s.trim().length > 0);

    // 选择最具描述性的句子
    const descriptiveSentence = sentences.find(s =>
      s.includes('看到') || s.includes('发现') || s.includes('遇到') ||
      s.includes('来到') || s.includes('走进') || s.includes('坐在')
    ) || sentences[0] || content.substring(0, 50);

    return descriptiveSentence.trim();
  }

  // 提取情绪氛围
  extractMood(content) {
    if (content.includes('开心') || content.includes('快乐')) return 'joyful';
    if (content.includes('温暖') || content.includes('友好')) return 'warm';
    if (content.includes('平静') || content.includes('安静')) return 'peaceful';
    if (content.includes('兴奋') || content.includes('激动')) return 'energetic';

    return 'friendly';
  }

  // 构建插图生成提示词
  buildIllustrationPrompt(page, storyData) {
    const baseStyle = `
儿童绘本插画风格，柔和的色彩，简洁清晰的线条，温暖友好的氛围，
适合${storyData.ageGroup}自闭症儿童的视觉感知特点，
避免过于复杂或混乱的背景，角色表情明确易识别
`;

    // 根据主题调整风格
    const themeStyles = {
      '人际关系': '强调友谊和社交互动，展现角色之间的友好交流',
      '家庭生活': '温馨的家庭场景，展现家庭成员之间的关爱',
      '法律常识': '安全有序的环境，展现规则和秩序的重要性',
      '人伦道德': '展现善良和道德品质，正面积极的价值观',
      '友谊': '强调友谊和合作，展现角色之间的友好关系'
    };

    const themeStyle = themeStyles[storyData.theme] || '积极正面的教育内容';

    // 分析页面内容，提取关键元素
    const content = page.content;
    const characters = this.extractCharacters(content);
    const actions = this.extractActions(content);
    const settings = this.extractSettings(content);
    const emotions = this.extractEmotions(content);

    // 构建完整提示词
    let prompt = `${baseStyle}, ${themeStyle}. `;
    
    if (characters.length > 0) {
      prompt += `角色: ${characters.join(', ')}. `;
    }
    
    if (settings.length > 0) {
      prompt += `场景: ${settings.join(', ')}. `;
    }
    
    if (actions.length > 0) {
      prompt += `动作: ${actions.join(', ')}. `;
    }
    
    if (emotions.length > 0) {
      prompt += `情感: ${emotions.join(', ')}. `;
    }
    
    // 添加页面内容的简化描述
    const simplifiedContent = this.simplifyContent(content);
    prompt += `场景描述: ${simplifiedContent}`;

    return prompt;
  }

  // 提取角色
  extractCharacters(content) {
    const characters = [];
    const characterKeywords = [
      '小熊', '波波', '小兔子', '小鸟', '小猫', '小狗', '小松鼠',
      '妈妈', '爸爸', '老师', '朋友', '同学', '警察', '医生'
    ];
    
    characterKeywords.forEach(char => {
      if (content.includes(char)) {
        characters.push(char);
      }
    });
    
    return characters;
  }

  // 提取动作
  extractActions(content) {
    const actions = [];
    const actionKeywords = [
      '走', '跑', '坐', '站', '唱歌', '跳舞', '玩耍', '学习', '吃饭',
      '睡觉', '读书', '画画', '帮助', '分享', '拥抱', '微笑', '思考'
    ];
    
    actionKeywords.forEach(action => {
      if (content.includes(action)) {
        actions.push(action);
      }
    });
    
    return actions;
  }

  // 提取场景
  extractSettings(content) {
    const settings = [];
    const settingKeywords = [
      '森林', '家', '学校', '公园', '草地', '河边', '山上', '花园',
      '教室', '操场', '图书馆', '厨房', '卧室', '客厅'
    ];
    
    settingKeywords.forEach(setting => {
      if (content.includes(setting)) {
        settings.push(setting);
      }
    });
    
    return settings;
  }

  // 提取情感
  extractEmotions(content) {
    const emotions = [];
    const emotionKeywords = [
      '开心', '快乐', '高兴', '兴奋', '满足', '温暖', '安心',
      '紧张', '担心', '害怕', '好奇', '惊讶', '感激', '勇敢'
    ];
    
    emotionKeywords.forEach(emotion => {
      if (content.includes(emotion)) {
        emotions.push(emotion);
      }
    });
    
    return emotions;
  }

  // 简化内容描述
  simplifyContent(content) {
    // 移除复杂的句子结构，保留核心描述
    let simplified = content
      .replace(/[。！？]/g, '. ')
      .replace(/[，、]/g, ', ')
      .substring(0, 100); // 限制长度
    
    if (content.length > 100) {
      simplified += '...';
    }
    
    return simplified;
  }

  // 检查服务是否可用
  isServiceAvailable() {
    return liblibService.isApiKeyInitialized();
  }

  // 获取生成状态
  getGenerationStatus() {
    return {
      isGenerating: this.isGenerating,
      queueLength: this.generationQueue.length
    };
  }
}

// 创建单例实例
const storyIllustrationGenerator = new StoryIllustrationGenerator();

export default storyIllustrationGenerator;
