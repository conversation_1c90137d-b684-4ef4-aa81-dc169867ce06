/**
 * 增强版提示词管理器 - 集成风格和人物一致性策略
 * 基于 style control.txt 文档的策略实现
 * 注意：不涉及API调用，只负责提示词生成和管理
 */

import StyleManager from './styleManager.js';
import CharacterManager from './characterManager.js';
import SceneStyleController from './sceneStyleController.js';
import ConsistencyTemplateManager from './consistencyTemplateManager.js';

class EnhancedPromptManager {
    constructor() {
        this.styleManager = new StyleManager();
        this.characterManager = new CharacterManager();
        this.sceneController = new SceneStyleController();
        this.consistencyManager = new ConsistencyTemplateManager();

        // 提示词历史记录
        this.promptHistory = [];

        // 当前故事上下文
        this.currentStoryContext = null;

        // 角色一致性模板缓存
        this.characterTemplates = {};
    }

    /**
     * 设置当前故事上下文
     * @param {Object} storyContext - 故事上下文信息
     */
    setStoryContext(storyContext) {
        this.currentStoryContext = storyContext;
    }

    /**
     * 为特定页面生成增强的图像提示词
     * @param {Object} pageInfo - 页面信息
     * @returns {Object} 增强的提示词对象
     */
    generateEnhancedImagePrompt(pageInfo) {
        const {
            pageNumber,
            content,
            sceneType = "indoor",
            characters = [],
            actions = "",
            isInteractive = false,
            interactionType = "question",
            emotion = "neutral",
            useReference = false,
            referenceImageUrl = null,
            uniqueElements = [],
            pageSpecificContent = "",
            storyData = {}
        } = pageInfo;

        console.log(`🎨 为第${pageNumber}页生成增强提示词...`);
        console.log(`📝 页面特定内容: "${pageSpecificContent}"`);
        console.log(`✨ 页面特有元素: ${uniqueElements.join(', ')}`);

        // 生成简洁的图像描述提示词
        const simplifiedPrompt = this.generateSimplifiedPrompt(pageInfo, storyData);

        // 构建增强的提示词对象
        const enhancedPrompt = {
            prompt: simplifiedPrompt,
            negativePrompt: this.generateNegativePrompt(),
            styleInfo: {
                artStyle: "children's book illustration",
                mood: 'friendly, warm',
                colorPalette: 'bright, soft colors'
            },
            sceneInfo: {
                sceneType,
                environment: this.extractEnvironment(content),
                mood: emotion
            },
            pageInfo: {
                pageNumber,
                isInteractive,
                sceneType,
                characters: characters.map(c => typeof c === 'string' ? c : c.name),
                uniqueElements,
                pageSpecificContent
            },
            generationParams: {
                useReference,
                referenceImageUrl,
                strength: useReference ? 0.5 : null,
                aspectRatio: "square",
                imgCount: 1,
                steps: 30
            }
        };

        console.log('🎭 简化提示词:', enhancedPrompt.prompt);

        // 记录提示词历史
        this.recordPromptHistory('simplified_image', {
            pageNumber,
            sceneType,
            characters,
            uniqueElements,
            pageSpecificContent,
            prompt: simplifiedPrompt,
            timestamp: Date.now()
        });

        return enhancedPrompt;
    }

    /**
     * 生成简化的图像描述提示词（基于OpenAI关键词）
     * @param {Object} pageInfo - 页面信息
     * @param {Object} storyData - 故事数据
     * @returns {string} 简化的提示词
     */
    generateSimplifiedPrompt(pageInfo, storyData) {
        const {
            pageNumber,
            content,
            characters = [],
            uniqueElements = [],
            pageSpecificContent = ""
        } = pageInfo;

        console.log(`🎨 生成简化提示词 - 第${pageNumber}页`);

        // 从故事数据中获取OpenAI生成的图像关键词
        const storyPage = storyData.pages?.find(p => p.id === pageNumber);
        const imageKeywords = storyPage?.image_keywords;

        if (imageKeywords) {
            console.log(`✅ 使用OpenAI生成的图像关键词:`, imageKeywords);
            return this.buildPromptFromKeywords(imageKeywords, pageNumber, storyData);
        }

        // 如果没有OpenAI关键词，使用传统方法生成
        console.log(`⚠️ 未找到OpenAI关键词，使用传统方法生成`);
        return this.buildTraditionalPrompt(pageInfo);
    }

    /**
     * 基于OpenAI关键词构建一致性图像生成提示词
     * @param {Object} keywords - 图像关键词
     * @param {number} pageNumber - 页面号
     * @param {Object} storyData - 故事数据（用于角色一致性）
     * @returns {string} 构建的提示词
     */
    buildPromptFromKeywords(keywords, pageNumber, storyData = null) {
        console.log(`🔧 处理OpenAI关键词:`, keywords);

        // 如果有故事数据，初始化角色模板
        if (storyData && storyData.characters && !Object.keys(this.characterTemplates).length) {
            this.characterTemplates = this.consistencyManager.generateCharacterTemplates(storyData.characters);
            console.log(`🎭 初始化角色一致性模板:`, this.characterTemplates);
        }

        // 检查是否应该使用一致性模板
        const shouldUseConsistencyTemplate = this.shouldUseConsistencyTemplate(keywords);

        if (shouldUseConsistencyTemplate) {
            return this.buildConsistentPrompt(keywords, pageNumber);
        } else {
            return this.buildStandardPrompt(keywords, pageNumber);
        }
    }

    /**
     * 判断是否应该使用一致性模板
     * @param {Object} keywords - 关键词
     * @returns {boolean} 是否使用一致性模板
     */
    shouldUseConsistencyTemplate(keywords) {
        // 如果有角色模板且关键词中包含角色，使用一致性模板
        return Object.keys(this.characterTemplates).length > 0 && keywords.characters && keywords.characters.length > 0;
    }

    /**
     * 使用一致性模板构建提示词
     * @param {Object} keywords - 关键词
     * @param {number} pageNumber - 页面号
     * @returns {string} 一致性提示词
     */
    buildConsistentPrompt(keywords, pageNumber) {
        const {
            characters = [],
            scene = "",
            mood = "happy",
            actions = [],
            objects = [],
            lighting = "soft natural lighting"
        } = keywords;

        // 映射场景类型
        const sceneType = this.mapSceneType(scene);

        // 构建一致性提示词参数
        const consistencyParams = {
            characterTemplates: this.characterTemplates,
            characters: characters.map(char => ({ name: char })), // 转换为对象格式
            actions,
            sceneType,
            mood,
            objects,
            pageNumber
        };

        const consistentPrompt = this.consistencyManager.buildConsistentPrompt(consistencyParams);
        console.log(`🎨 一致性模板生成的提示词: "${consistentPrompt}"`);

        return consistentPrompt;
    }

    /**
     * 使用标准方法构建提示词
     * @param {Object} keywords - 关键词
     * @param {number} pageNumber - 页面号
     * @returns {string} 标准提示词
     */
    buildStandardPrompt(keywords, pageNumber) {
        const {
            characters = [],
            scene = "",
            mood = "",
            actions = [],
            objects = [],
            lighting = "soft natural lighting",
            style = "children's book illustration, watercolor style, soft pastel colors"
        } = keywords;

        // 构建专业的图像生成提示词
        let promptParts = [];

        // 1. 主要主题和角色
        if (characters.length > 0) {
            promptParts.push(characters.join(", "));
        }

        // 2. 动作描述
        if (actions.length > 0) {
            promptParts.push(actions.join(", "));
        }

        // 3. 场景环境
        if (scene) {
            promptParts.push(`in ${scene}`);
        }

        // 4. 场景物品
        if (objects.length > 0) {
            promptParts.push(`with ${objects.join(", ")}`);
        }

        // 5. 情绪和氛围
        if (mood) {
            promptParts.push(mood);
        }

        // 6. 光照效果
        if (lighting) {
            promptParts.push(lighting);
        }

        // 7. 艺术风格
        promptParts.push(style);

        // 8. 质量和技术参数
        promptParts.push("high quality, masterpiece, detailed illustration");

        // 9. 页面标识
        promptParts.push(`page ${pageNumber} unique composition`);

        // 组合所有部分
        const prompt = promptParts.join(", ");

        console.log(`🎨 标准方法生成的提示词: "${prompt}"`);
        return prompt;
    }

    /**
     * 映射场景类型
     * @param {string} scene - 场景描述
     * @returns {string} 场景类型
     */
    mapSceneType(scene) {
        const sceneMapping = {
            'home': ['home', 'house', 'kitchen', 'bedroom', 'living room'],
            'garden': ['garden', 'flower', 'outdoor', 'yard'],
            'forest': ['forest', 'tree', 'woods', 'nature'],
            'school': ['school', 'classroom', 'desk', 'blackboard'],
            'park': ['park', 'playground', 'grass', 'bench']
        };

        for (const [type, keywords] of Object.entries(sceneMapping)) {
            if (keywords.some(keyword => scene.toLowerCase().includes(keyword))) {
                return type;
            }
        }

        return 'garden'; // 默认场景
    }

    /**
     * 传统方法生成提示词（回退方案）
     * @param {Object} pageInfo - 页面信息
     * @returns {string} 传统提示词
     */
    buildTraditionalPrompt(pageInfo) {
        const {
            pageNumber,
            characters = [],
            sceneType = "indoor",
            uniqueElements = [],
            pageSpecificContent = ""
        } = pageInfo;

        let prompt = "children's book illustration";

        // 添加角色
        if (characters.length > 0) {
            const characterNames = characters.map(c =>
                typeof c === 'string' ? c : c.name
            ).join(", ");
            prompt += `, featuring ${characterNames}`;
        }

        // 添加场景
        prompt += `, ${sceneType} scene`;

        // 添加特有元素
        if (uniqueElements.length > 0) {
            const elements = uniqueElements
                .filter(el => !el.startsWith('page'))
                .join(", ");
            if (elements) {
                prompt += `, with ${elements}`;
            }
        }

        // 添加特定内容
        if (pageSpecificContent) {
            prompt += `, showing ${pageSpecificContent}`;
        }

        // 添加风格
        prompt += `, warm children's illustration style, soft colors`;

        // 添加页面标识
        prompt += `, page ${pageNumber}`;

        console.log(`🎨 传统方法提示词: "${prompt}"`);
        return prompt;
    }

    /**
     * 生成专业的负面提示词
     * @param {boolean} useConsistencyTemplate - 是否使用一致性模板
     * @returns {string} 负面提示词
     */
    generateNegativePrompt(useConsistencyTemplate = false) {
        if (useConsistencyTemplate && this.consistencyManager) {
            return this.consistencyManager.generateConsistentNegativePrompt();
        }

        return "dark, scary, frightening, horror, violence, weapons, blood, gore, adult content, sexual content, inappropriate, complex patterns, overwhelming details, cluttered background, realistic photography, harsh lighting, abstract art, inconsistent style, adult themes, text, words, letters, signatures, watermarks, blurry, low quality, distorted, deformed, ugly, bad anatomy, extra limbs, missing limbs, floating limbs, disconnected limbs, malformed hands, poorly drawn hands, mutated hands, extra fingers, fewer fingers, long neck, cross-eyed, mutated, mutation, deformed, poorly drawn, bad anatomy, disfigured, lowres, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry";
    }

    /**
     * 提取环境描述
     * @param {string} content - 页面内容
     * @returns {string} 环境描述
     */
    extractEnvironment(content) {
        const environments = {
            '家里': 'cozy home interior',
            '学校': 'friendly school environment',
            '公园': 'sunny park setting',
            '森林': 'peaceful forest',
            '花园': 'beautiful garden',
            '教室': 'bright classroom',
            '操场': 'playground',
            '图书馆': 'quiet library'
        };

        for (const [chinese, english] of Object.entries(environments)) {
            if (content.includes(chinese)) {
                return english;
            }
        }

        return 'peaceful indoor setting';
    }

    /**
     * 生成角色种子图像的提示词
     * @param {string} characterName - 角色名称
     * @returns {Object} 种子图像提示词
     */
    generateCharacterSeedPrompt(characterName) {
        const seedDescription = this.characterManager.generateSeedImageDescription(characterName);
        const stylePrompt = this.styleManager.generateStylePrompt(seedDescription);

        // 添加种子图像特有的负面提示词
        const seedNegativePrompts = [
            "side view",
            "back view", 
            "complex pose",
            "busy background",
            "multiple characters",
            "action scenes"
        ];

        const enhancedPrompt = {
            prompt: stylePrompt.prompt,
            negativePrompt: `${stylePrompt.negativePrompt}, ${seedNegativePrompts.join(", ")}`,
            characterInfo: {
                name: characterName,
                isSeedImage: true,
                features: this.characterManager.getCharacterKeyFeatures(characterName)
            },
            generationParams: {
                aspectRatio: "square",
                imgCount: 3, // 生成3个候选
                steps: 35,
                seed: 42 // 固定种子确保可重现性
            }
        };

        this.recordPromptHistory('character_seed', enhancedPrompt);
        return enhancedPrompt;
    }

    /**
     * 生成基于用户回答的个性化插画提示词
     * @param {Object} answerInfo - 用户回答信息
     * @returns {Object} 个性化插画提示词
     */
    generatePersonalizedIllustrationPrompt(answerInfo) {
        const {
            userAnswer,
            questionContext,
            pageNumber,
            baseCharacters = [],
            emotion = "happy"
        } = answerInfo;

        // 分析用户回答，提取关键元素
        const answerElements = this.analyzeUserAnswer(userAnswer);
        
        // 构建个性化场景描述
        const personalizedScene = this.buildPersonalizedScene(answerElements, questionContext, baseCharacters);

        // 生成场景提示词
        const scenePrompt = this.sceneController.generateScenePrompt(
            "interaction",
            baseCharacters.map(char => ({ ...char, emotion })),
            personalizedScene
        );

        const enhancedPrompt = {
            ...scenePrompt,
            personalizationInfo: {
                userAnswer,
                answerElements,
                pageNumber,
                isPersonalized: true
            },
            generationParams: {
                useReference: true, // 使用参考图像保持一致性
                strength: 0.5, // 较低强度保持个性化
                aspectRatio: "square",
                imgCount: 1,
                steps: 30
            }
        };

        this.recordPromptHistory('personalized', enhancedPrompt);
        return enhancedPrompt;
    }

    /**
     * 生成故事内容提示词
     * @param {Object} storyParams - 故事参数
     * @returns {string} 故事生成提示词
     */
    generateStoryPrompt(storyParams) {
        const {
            theme = "友谊",
            ageRange = "6-8",
            characterNames = ["小熊波波", "小兔子"],
            pageCount = 12,
            interactivePages = [4, 8, 11]
        } = storyParams;

        const storyPrompt = `
请为${ageRange}岁的自闭症儿童创作一个以"${theme}"为主题的${pageCount}页绘本故事。

主要角色：${characterNames.join("、")}

要求：
1. 故事应该简单、清晰，使用具体而非抽象的语言
2. 每页内容控制在100-150字左右
3. 使用简单的句子结构和明确的因果关系
4. 避免使用隐喻、反语或复杂的修辞手法
5. 故事中应包含明确的情感表达和社交互动场景
6. 在第${interactivePages.join("、")}页安排交互环节，设计开放性问题
7. 每个交互问题都需要配备引导提示
8. 确保角色性格一致，${characterNames[0]}是主角，性格害羞但善良
9. 故事应该有明确的友谊主题和积极的结局

输出格式：
{
  "title": "故事标题",
  "theme": "${theme}",
  "characters": [${characterNames.map(name => `"${name}"`).join(", ")}],
  "pages": [
    {
      "page_number": 1,
      "content": "第1页内容...",
      "scene_type": "forest|indoor|playground|home",
      "characters": [{"name": "角色名", "emotion": "情绪", "action": "动作"}],
      "is_interactive": false
    },
    {
      "page_number": ${interactivePages[0]},
      "content": "第${interactivePages[0]}页内容...",
      "scene_type": "interaction",
      "characters": [{"name": "角色名", "emotion": "情绪", "action": "动作"}],
      "is_interactive": true,
      "interaction_type": "question",
      "interactive_question": "问题内容...",
      "guidance_prompt": "引导提示内容..."
    }
    // 其他页面...
  ]
}`;

        this.recordPromptHistory('story', { prompt: storyPrompt, params: storyParams });
        return storyPrompt;
    }

    /**
     * 分析用户回答，提取关键元素
     * @param {string} userAnswer - 用户回答
     * @returns {Object} 分析结果
     */
    analyzeUserAnswer(userAnswer) {
        // 简化的回答分析逻辑
        const elements = {
            emotions: [],
            actions: [],
            objects: [],
            relationships: [],
            settings: []
        };

        // 情绪关键词
        const emotionKeywords = {
            happy: ["开心", "高兴", "快乐", "喜欢"],
            sad: ["难过", "伤心", "不开心"],
            excited: ["兴奋", "激动", "期待"],
            shy: ["害羞", "不好意思", "紧张"],
            curious: ["好奇", "想知道", "疑问"]
        };

        // 检测情绪
        for (const [emotion, keywords] of Object.entries(emotionKeywords)) {
            if (keywords.some(keyword => userAnswer.includes(keyword))) {
                elements.emotions.push(emotion);
            }
        }

        // 检测动作
        const actionKeywords = ["玩", "跑", "走", "坐", "说话", "拥抱", "帮助"];
        elements.actions = actionKeywords.filter(action => userAnswer.includes(action));

        return elements;
    }

    /**
     * 构建个性化场景描述
     * @param {Object} answerElements - 回答元素
     * @param {string} questionContext - 问题上下文
     * @param {Array} baseCharacters - 基础角色
     * @returns {string} 个性化场景描述
     */
    buildPersonalizedScene(answerElements, questionContext, baseCharacters) {
        const sceneElements = [];

        // 添加角色动作
        if (answerElements.actions.length > 0) {
            sceneElements.push(`characters ${answerElements.actions.join(" and ")}`);
        }

        // 添加情绪表达
        if (answerElements.emotions.length > 0) {
            sceneElements.push(`showing ${answerElements.emotions.join(" and ")} emotions`);
        }

        // 添加上下文相关元素
        sceneElements.push(`in response to the question about ${questionContext}`);

        return sceneElements.join(", ");
    }

    /**
     * 记录提示词历史
     * @param {string} type - 提示词类型
     * @param {Object} promptData - 提示词数据
     */
    recordPromptHistory(type, promptData) {
        const historyEntry = {
            timestamp: Date.now(),
            type,
            data: promptData
        };

        this.promptHistory.push(historyEntry);

        // 保持最近50条记录
        if (this.promptHistory.length > 50) {
            this.promptHistory = this.promptHistory.slice(-50);
        }
    }

    /**
     * 获取提示词历史
     * @param {string} type - 可选的类型过滤
     * @returns {Array} 提示词历史
     */
    getPromptHistory(type = null) {
        if (type) {
            return this.promptHistory.filter(entry => entry.type === type);
        }
        return this.promptHistory;
    }

    /**
     * 清除提示词历史
     */
    clearPromptHistory() {
        this.promptHistory = [];
    }

    /**
     * 获取管理器状态
     * @returns {Object} 管理器状态信息
     */
    getManagerStatus() {
        return {
            hasStoryContext: !!this.currentStoryContext,
            promptHistoryCount: this.promptHistory.length,
            availableCharacters: this.characterManager.getAllCharacters(),
            lastPromptTime: this.promptHistory.length > 0 ? 
                this.promptHistory[this.promptHistory.length - 1].timestamp : null
        };
    }
}

export default EnhancedPromptManager;
