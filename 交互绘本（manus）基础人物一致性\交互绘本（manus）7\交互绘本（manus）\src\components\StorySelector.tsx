// 故事选择器组件
// 提供故事列表、选择和管理功能

import { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Alert, AlertDescription } from './ui/alert';
import { Badge } from './ui/badge';
import storyManager from '../services/storyManager';

interface StorySelectorProps {
  onStorySelected: (storyData: any) => void;
  onGenerateNew: () => void;
  onCancel: () => void;
}

export function StorySelector({ onStorySelected, onGenerateNew, onCancel }: StorySelectorProps) {
  const [stories, setStories] = useState<any[]>([]);
  const [selectedStoryId, setSelectedStoryId] = useState<string>('');
  const [stats, setStats] = useState<any>(null);

  useEffect(() => {
    loadStories();
  }, []);

  const loadStories = () => {
    const allStories = storyManager.getAllStories();
    const currentStory = storyManager.getCurrentStory();
    const storyStats = storyManager.getStoryStats();
    
    setStories(allStories);
    setSelectedStoryId(currentStory?.id || '');
    setStats(storyStats);
  };

  const handleStorySelect = (storyId: string) => {
    setSelectedStoryId(storyId);
  };

  const handleConfirmSelection = () => {
    if (selectedStoryId) {
      storyManager.switchToStory(selectedStoryId);
      const selectedStory = storyManager.getCurrentStory();
      onStorySelected(selectedStory);
    }
  };

  const handleDeleteStory = (storyId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    
    if (confirm('确定要删除这个故事吗？此操作不可撤销。')) {
      const success = storyManager.deleteStory(storyId);
      if (success) {
        loadStories(); // 重新加载故事列表
      }
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getThemeColor = (theme: string) => {
    const colors = {
      '友谊': 'bg-blue-100 text-blue-800',
      '人际关系': 'bg-green-100 text-green-800',
      '家庭生活': 'bg-pink-100 text-pink-800',
      '法律常识': 'bg-purple-100 text-purple-800',
      '人伦道德': 'bg-orange-100 text-orange-800'
    };
    return colors[theme] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4 bg-amber-50">
      <Card className="max-w-4xl w-full">
        <CardHeader>
          <CardTitle className="text-3xl text-center">
            📚 故事库
          </CardTitle>
          <p className="text-center text-gray-600">
            选择一个故事开始阅读，或生成新的故事
          </p>
        </CardHeader>
        <CardContent>
          {/* 统计信息 */}
          {stats && (
            <div className="mb-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-semibold mb-2">故事库统计</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">总故事数：</span>
                  <span className="font-medium">{stats.totalStories}</span>
                </div>
                <div>
                  <span className="text-gray-600">生成故事：</span>
                  <span className="font-medium">{stats.generatedStories}</span>
                </div>
                <div>
                  <span className="text-gray-600">当前故事：</span>
                  <span className="font-medium">{stats.currentStory}</span>
                </div>
                <div>
                  <span className="text-gray-600">主题分布：</span>
                  <span className="font-medium">
                    {Object.keys(stats.themeDistribution).length} 种
                  </span>
                </div>
              </div>
            </div>
          )}

          {/* 故事列表 */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-4">选择故事</h3>
            {stories.length === 0 ? (
              <Alert>
                <AlertDescription>
                  暂无可用故事，请生成新故事。
                </AlertDescription>
              </Alert>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                {stories.map((story) => (
                  <Card 
                    key={story.id}
                    className={`cursor-pointer transition-all hover:shadow-md ${
                      selectedStoryId === story.id 
                        ? 'ring-2 ring-blue-500 bg-blue-50' 
                        : 'hover:bg-gray-50'
                    }`}
                    onClick={() => handleStorySelect(story.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h4 className="font-medium text-lg">{story.title}</h4>
                            {story.isDefault && (
                              <Badge variant="secondary" className="text-xs">
                                默认
                              </Badge>
                            )}
                          </div>
                          
                          <div className="flex items-center gap-2 mb-2">
                            <Badge className={`text-xs ${getThemeColor(story.theme)}`}>
                              {story.theme}
                            </Badge>
                            <span className="text-xs text-gray-500">
                              {story.ageGroup}
                            </span>
                          </div>
                          
                          <p className="text-sm text-gray-600 mb-2">
                            {story.pages?.length || 0} 页 • 
                            {story.pages?.filter((p: any) => p.isInteractive).length || 0} 个交互
                          </p>
                          
                          <p className="text-xs text-gray-500">
                            创建于 {formatDate(story.createdAt)}
                          </p>
                        </div>
                        
                        <div className="flex flex-col gap-1">
                          {selectedStoryId === story.id && (
                            <Badge variant="default" className="text-xs">
                              已选择
                            </Badge>
                          )}
                          
                          {!story.isDefault && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="text-red-600 hover:text-red-800 hover:bg-red-50 p-1 h-auto"
                              onClick={(e) => handleDeleteStory(story.id, e)}
                              title="删除故事"
                            >
                              🗑️
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-center gap-4">
            <Button 
              onClick={handleConfirmSelection}
              disabled={!selectedStoryId}
              size="lg"
            >
              📖 开始阅读
            </Button>
            
            <Button 
              onClick={onGenerateNew}
              variant="outline"
              size="lg"
            >
              🎨 生成新故事
            </Button>
            
            <Button 
              onClick={onCancel}
              variant="ghost"
              size="lg"
            >
              返回
            </Button>
          </div>

          {/* 功能说明 */}
          <div className="mt-8 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium mb-2">使用说明</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• 点击故事卡片选择要阅读的故事</li>
              <li>• 默认故事是经过精心设计的《小熊波波的友谊冒险》</li>
              <li>• 生成的故事会自动保存，可以随时切换阅读</li>
              <li>• 可以删除不需要的生成故事（默认故事不可删除）</li>
              <li>• 每个故事都包含12页内容和3个交互环节</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
